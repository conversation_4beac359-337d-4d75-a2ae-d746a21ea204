package sealed;

public class SealedApp {

    void generate(Animal animal) {
        switch (animal) {
            case Lion(var carnivore) -> System.out.println("Lion is carnivore ? " + (carnivore ? "yes" : "no"));
            case Cat(var pet) -> System.out.println("Cat is pet ? " + (pet ? "yes" : "no"));
        }
    }

    public static void main(String[] args) {
        var cat = new Cat(true);
        var lion = new Lion(false);
        var app = new SealedApp();
        app.generate(cat);
        app.generate(lion);
    }
}
