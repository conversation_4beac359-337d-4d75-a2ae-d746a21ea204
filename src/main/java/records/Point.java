package records;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Path;

record Point(int x, int y) implements Serializable {

    Point {
        if (x < 0 || y < 0) {
            throw new IllegalArgumentException("x and y must be positive");
        }
    }

    Point(int z) {
        this(z, z * z);
    }

    public void save() throws IOException {
        try (var file = Files.newOutputStream(Path.of("point.dat"))) {
            var objectStream = new ObjectOutputStream(file);
            objectStream.writeObject(this);
        }
    }

    public Point read() {
        Point point = null;

        try (var file = Files.newInputStream(Path.of("point.dat"))) {
            var objectStream = new ObjectInputStream(file);
            point = (Point) objectStream.readObject();
        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
        }

        return point;
    }
}
