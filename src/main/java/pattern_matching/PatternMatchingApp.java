package pattern_matching;

public class PatternMatchingApp {

    public void surface(Shape shape) {
        switch (shape) {
            case Square square -> System.out.println("surface square: " + square.side() * square.side());
            case Circle circle ->
                    System.out.println("surface circle: " + circle.diameter() * circle.diameter() * Math.PI);
            default -> System.out.println("Unknown shape");
        }
    }

    public void surface_v2(Shape shape) {
        switch (shape) {
            case Square(var side) -> System.out.println("surface_v2 square: " + side * side);
            case Circle(var diameter) -> System.out.println("surface_v2 circle: " + diameter * diameter * Math.PI);
            default -> System.out.println("Unknown shape");
        }
    }

    public static void main(String[] args) {
        var inst = new PatternMatchingApp();
        inst.surface(new Square(10));
        inst.surface_v2(new Circle(20));
    }
}
